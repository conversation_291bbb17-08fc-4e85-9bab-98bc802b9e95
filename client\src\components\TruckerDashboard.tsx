import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import SignatureCanvas from 'react-signature-canvas';
import imageCompression from 'browser-image-compression';
import LanguageSwitcher from './LanguageSwitcher';

interface MaterialItem {
  _id: string;
  material: string;
  quantityOrdered: number;
  quantityDelivered: number;
  unit: string;
  isOverDelivered: boolean;
}

interface DeliveryLocation {
  _id: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  materials: MaterialItem[];
  status: 'pending' | 'pickup_accepted' | 'pickup_completed' | 'en_route' | 'completed';
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  pickupCompletedAt?: string;
  completedAt?: string;
  pickupPhoto?: string;
  pickupSignature?: string;
  deliveryPhoto?: string;
  signature?: string;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  client: string;
  title: string;
  description?: string;
  pickupLocation: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  } | string; // Support both old and new format
  deliveryLocations: DeliveryLocation[];
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  haulingRate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
  notes?: string;
}

interface Transaction {
  _id: string;
  purchaseOrderId: string;
  orderNumber: string;
  trucker?: {
    _id: string;
    username: string;
    firstName: string;
    lastName: string;
  } | null;
  deliveryLocationId: string;
  deliveryAddress: string;
  materialsDelivered: {
    material: string;
    quantityDelivered: number;
    unit: string;
    ratePerUnit: number;
  }[];
  deliveryPhoto: string;
  signature: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
  completedAt: string;
  createdAt: string;
}

const TruckerDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { t } = useTranslation();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);

  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number} | null>(null);
  const [locationError, setLocationError] = useState<string>('');
  const [showPickupModal, setShowPickupModal] = useState(false);
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [selectedDeliveryLocation, setSelectedDeliveryLocation] = useState<DeliveryLocation | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [pickupPhoto, setPickupPhoto] = useState<string>('');
  const [pickupSignature, setPickupSignature] = useState<string>('');
  const [deliveryPhoto, setDeliveryPhoto] = useState<string>('');
  const [deliverySignature, setDeliverySignature] = useState<string>('');
  const [actualQuantities, setActualQuantities] = useState<string[]>([]);
  const [deliveryNotes, setDeliveryNotes] = useState<string>('');
  const pickupSignatureRef = useRef<SignatureCanvas>(null);
  const deliverySignatureRef = useRef<SignatureCanvas>(null);
  const [developerMode, setDeveloperMode] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'orders' | 'transactions'>('orders');

  const API_BASE_URL = '/api';

  useEffect(() => {
    fetchOrders();
    getCurrentLocation();
    if (activeTab === 'transactions') {
      fetchTransactions();
    }
  }, [activeTab]);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationError('');
        },
        (error) => {
          setLocationError('Unable to get your location. Please enable location services.');
          console.error('Geolocation error:', error);
        }
      );
    } else {
      setLocationError('Geolocation is not supported by this browser.');
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders/transactions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.data.success) {
        setTransactions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handleAcceptPickup = (orderId: string, deliveryId: string) => {
    if (!developerMode && !currentLocation) {
      alert(t('dashboard.locationError'));
      getCurrentLocation();
      return;
    }

    // Find the order and delivery location
    const order = orders.find(o => o._id === orderId);
    const deliveryLocation = order?.deliveryLocations.find(dl => dl._id === deliveryId);

    if (!order || !deliveryLocation) {
      alert('Order or delivery location not found');
      return;
    }

    setSelectedOrder(order);
    setSelectedDeliveryLocation(deliveryLocation);
    setShowPickupModal(true);
  };

  const handleSubmitPickup = async () => {
    if (!selectedOrder || !selectedDeliveryLocation) {
      alert('Missing order or delivery location information');
      return;
    }

    if (!pickupPhoto || !pickupSignature) {
      alert(t('pickup.pickupPhotoRequired') + ' and ' + t('pickup.pickupSignatureRequired'));
      return;
    }

   try {
  console.log('Sending pickup data:', {
    latitude: currentLocation?.lat,
    longitude: currentLocation?.lng,
    pickupPhoto,
    pickupSignature,
    developerMode
  });

  const response = await axios.patch(`${API_BASE_URL}/purchase-orders/${selectedOrder._id}/accept-pickup/${selectedDeliveryLocation._id}`, {
    latitude: currentLocation?.lat || 0,
    longitude: currentLocation?.lng || 0,
    pickupPhoto,
    pickupSignature,
    developerMode
  });

  // ...rest of your logic


      if (response.data.success) {
        fetchOrders(); // Refresh orders
        alert(response.data.message);
        setShowPickupModal(false);
        setSelectedOrder(null);
        setSelectedDeliveryLocation(null);
        setPickupPhoto('');
        setPickupSignature('');
        if (pickupSignatureRef.current) {
          pickupSignatureRef.current.clear();
        }
      }
    } catch (error: any) {
      console.error('Error accepting pickup:', error);
      const errorMessage = error.response?.data?.message || 'Error accepting pickup';
      alert(errorMessage);
    }
  };

  const handleCompletePickup = async (orderId: string, deliveryId: string) => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/purchase-orders/${orderId}/complete-pickup/${deliveryId}`);

      if (response.data.success) {
        fetchOrders(); // Refresh orders
        alert(response.data.message);
      }
    } catch (error: any) {
      console.error('Error completing pickup:', error);
      const errorMessage = error.response?.data?.message || 'Error completing pickup';
      alert(errorMessage);
    }
  };

  const handleCompleteDelivery = async () => {
    if (!selectedDeliveryLocation || (!developerMode && !currentLocation)) {
      alert('Missing required information');
      return;
    }

    if (!deliveryPhoto || !deliverySignature) {
      alert(t('delivery.photoRequired') + ' and ' + t('delivery.signatureRequired'));
      return;
    }

    if (!actualQuantities || actualQuantities.length === 0) {
      alert('Please specify the actual quantities delivered');
      return;
    }

    // Validate actual quantities
    for (let i = 0; i < actualQuantities.length; i++) {
      const qty = actualQuantities[i];
      if (!qty || parseFloat(qty) < 0) {
        alert('Please enter valid quantities for all materials (0 or greater)');
        return;
      }
    }

    // Find the order that contains this delivery location
    const activeOrder = orders.find(order =>
      order.deliveryLocations?.some(dl =>
        dl._id === selectedDeliveryLocation._id &&
        dl.assignedTo?.username === user?.username
      )
    );

    if (!activeOrder) {
      alert('No active order found');
      return;
    }

    try {
      const response = await axios.patch(
        `${API_BASE_URL}/purchase-orders/${activeOrder._id}/complete-delivery/${selectedDeliveryLocation._id}`,
        {
          latitude: currentLocation?.lat || 0,
          longitude: currentLocation?.lng || 0,
          deliveryPhoto,
          deliverySignature,
          actualQuantities,
          notes: deliveryNotes,
          developerMode
        }
      );

      if (response.data.success) {
        let message = response.data.message;
        if (response.data.warnings && response.data.warnings.length > 0) {
          message += '\n\nWarnings:\n' + response.data.warnings.join('\n');
        }
        alert(message);
        fetchOrders(); // Refresh orders
        setShowDeliveryModal(false);
        setSelectedDeliveryLocation(null);
        setDeliveryPhoto('');
        setDeliverySignature('');
        setActualQuantities([]);
        setDeliveryNotes('');
        if (deliverySignatureRef.current) {
          deliverySignatureRef.current.clear();
        }
      }
    } catch (error: any) {
      console.error('Error completing delivery:', error);
      const errorMessage = error.response?.data?.message || 'Error completing delivery';
      alert(errorMessage);
    }
  };

  const handlePickupPhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        // Compression options
        const options = {
          maxSizeMB: 1, // Maximum file size in MB
          maxWidthOrHeight: 1920, // Maximum width or height
          useWebWorker: true,
          fileType: 'image/jpeg' as const,
          initialQuality: 0.8
        };

        // Compress the image
        const compressedFile = await imageCompression(file, options);

        // Convert to base64
        const reader = new FileReader();
        reader.onload = (e) => {
          setPickupPhoto(e.target?.result as string);
        };
        reader.readAsDataURL(compressedFile);

        console.log('Original pickup file size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
        console.log('Compressed pickup file size:', (compressedFile.size / 1024 / 1024).toFixed(2), 'MB');
      } catch (error) {
        console.error('Error compressing pickup image:', error);
        alert('Error processing pickup image. Please try a different image.');
      }
    }
  };

  const handleDeliveryPhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        // Compression options
        const options = {
          maxSizeMB: 1, // Maximum file size in MB
          maxWidthOrHeight: 1920, // Maximum width or height
          useWebWorker: true,
          fileType: 'image/jpeg' as const,
          initialQuality: 0.8
        };

        // Compress the image
        const compressedFile = await imageCompression(file, options);

        // Convert to base64
        const reader = new FileReader();
        reader.onload = (e) => {
          setDeliveryPhoto(e.target?.result as string);
        };
        reader.readAsDataURL(compressedFile);

        console.log('Original delivery file size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
        console.log('Compressed delivery file size:', (compressedFile.size / 1024 / 1024).toFixed(2), 'MB');
      } catch (error) {
        console.error('Error compressing delivery image:', error);
        alert('Error processing delivery image. Please try a different image.');
      }
    }
  };

  const clearPickupSignature = () => {
    if (pickupSignatureRef.current) {
      pickupSignatureRef.current.clear();
    }
    setPickupSignature('');
  };

  const savePickupSignature = () => {
    if (pickupSignatureRef.current) {
      const signatureData = pickupSignatureRef.current.toDataURL();
      setPickupSignature(signatureData);
    }
  };

  const clearDeliverySignature = () => {
    if (deliverySignatureRef.current) {
      deliverySignatureRef.current.clear();
    }
    setDeliverySignature('');
  };

  const saveDeliverySignature = () => {
    if (deliverySignatureRef.current) {
      const signatureData = deliverySignatureRef.current.toDataURL();
      setDeliverySignature(signatureData);
    }
  };

  // Initialize actual quantities when delivery location is selected
  const handleDeliveryLocationSelect = (location: DeliveryLocation) => {
    setSelectedDeliveryLocation(location);
    // Initialize actual quantities array based on materials in the order
    if (location.materials && location.materials.length > 0) {
      setActualQuantities(new Array(location.materials.length).fill(''));
    } else {
      setActualQuantities(['']);
    }
    setShowDeliveryModal(true);
  };

  const updateActualQuantity = (index: number, value: string) => {
    const updated = [...actualQuantities];
    updated[index] = value;
    setActualQuantities(updated);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'pickup_completed': return 'bg-info';
      case 'en_route': return 'bg-info';
      case 'delivery_in_progress': return 'bg-primary';
      case 'delivered': return 'bg-success';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  const getPickupLocationAddress = (pickupLocation: any): string => {
    if (typeof pickupLocation === 'string') {
      return pickupLocation;
    }
    return pickupLocation?.address || 'Unknown location';
  };

  const getDeliveryLocationsDisplay = (deliveryLocations: DeliveryLocation[]): string => {
    if (!deliveryLocations || deliveryLocations.length === 0) {
      return 'No delivery locations';
    }
    if (deliveryLocations.length === 1) {
      return deliveryLocations[0].address;
    }
    return `${deliveryLocations.length} locations`;
  };

  const pendingOrders = orders.filter(order =>
    order.deliveryLocations?.some(dl => dl.status === 'pending')
  );
  const myOrders = orders.filter(order =>
    order.deliveryLocations?.some(dl => dl.assignedTo?.username === user?.username)
  );

  // Debug logging
  console.log('Total orders:', orders.length);
  console.log('Pending orders:', pendingOrders.length);
  console.log('My orders:', myOrders.length);
  if (orders.length > 0) {
    console.log('First order delivery locations:', orders[0].deliveryLocations);
    console.log('First order delivery location statuses:', orders[0].deliveryLocations?.map(dl => dl.status));
  }
  const hasActiveOrder = myOrders.some(order =>
    order.deliveryLocations?.some(dl =>
      dl.assignedTo?.username === user?.username &&
      ['pickup_accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'].includes(dl.status || '')
    )
  );

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-truck me-2"></i>
            {t('dashboard.truckerDashboard')}
          </span>
          <div className="navbar-nav ms-auto d-flex align-items-center">
            <div className="form-check form-switch me-3">
              <input
                className="form-check-input"
                type="checkbox"
                id="developerMode"
                checked={developerMode}
                onChange={(e) => setDeveloperMode(e.target.checked)}
              />
              <label className="form-check-label text-white" htmlFor="developerMode">
                <small>{t('delivery.developerMode')}</small>
              </label>
            </div>
            {developerMode && (
              <small className="badge bg-warning text-dark me-3">
                <i className="bi bi-exclamation-triangle me-1"></i>
                {t('delivery.bypassGeofencing')}
              </small>
            )}
            <span className="navbar-text me-3">
              {t('dashboard.welcome')}, {user?.firstName} {user?.lastName}
            </span>
            <div className="me-3">
              <LanguageSwitcher />
            </div>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              {t('auth.logout')}
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Statistics */}
        <div className="row mb-4">
          <div className="col-12 col-md-4 mb-3 mb-md-0">
            <div className="card bg-warning text-white">
              <div className="card-body text-center">
                <h6 className="card-title">{t('statistics.availableOrders')}</h6>
                <h3 className="mb-0">{pendingOrders.length}</h3>
              </div>
            </div>
          </div>
          <div className="col-12 col-md-4 mb-3 mb-md-0">
            <div className="card bg-success text-white">
              <div className="card-body text-center">
                <h6 className="card-title">{t('statistics.myActiveOrders')}</h6>
                <h3 className="mb-0">{myOrders.filter(o => o.status !== 'delivered').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-12 col-md-4">
            <div className="card bg-primary text-white">
              <div className="card-body text-center">
                <h6 className="card-title">{t('statistics.completedOrders')}</h6>
                <h3 className="mb-0">{myOrders.filter(o => o.status === 'delivered').length}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Current Location Display */}
        <div className="row mb-4">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">
                  <i className="bi bi-geo-alt me-2"></i>
                  {t('dashboard.currentLocation')}
                </h6>
              </div>
              <div className="card-body">
                {locationError ? (
                  <div className="alert alert-warning" role="alert">
                    <i className="bi bi-exclamation-triangle me-2"></i>
                    <div className="d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
                      <span className="mb-2 mb-sm-0">{locationError}</span>
                      <button className="btn btn-sm btn-outline-warning ms-sm-2" onClick={getCurrentLocation}>
                        {t('dashboard.enableLocation')}
                      </button>
                    </div>
                  </div>
                ) : currentLocation ? (
                  <div className="row">
                    <div className="col-12 col-md-6 mb-2 mb-md-0">
                      <small className="text-muted">{t('dashboard.latitude')}:</small>
                      <div className="fw-bold">{currentLocation.lat.toFixed(6)}</div>
                    </div>
                    <div className="col-12 col-md-6">
                      <small className="text-muted">{t('dashboard.longitude')}:</small>
                      <div className="fw-bold">{currentLocation.lng.toFixed(6)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                    <small className="text-muted">{t('dashboard.gettingLocation')}</small>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="row mb-4">
          <div className="col">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'orders' ? 'active' : ''}`}
                  onClick={() => setActiveTab('orders')}
                >
                  <i className="bi bi-truck me-2"></i>
                  {t('navigation.orders')}
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'transactions' ? 'active' : ''}`}
                  onClick={() => setActiveTab('transactions')}
                >
                  <i className="bi bi-receipt me-2"></i>
                  {t('transactions.myTransactions')}
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'orders' && (
          <>
             {/* Available Orders or Active Order */}
            <div className="row mb-4">
              <div className="col">
                <div className="card">
                  <div className="card-header">
                    <h5 className="mb-0">
                      <i className="bi bi-list-ul me-2"></i>
                      {hasActiveOrder ? t('orders.myActiveOrder') : t('orders.availableOrders')}
                    </h5>
                  </div>
                  <div className="card-body">

                {hasActiveOrder ? (
                  // Show active order details
                  myOrders.filter(order =>
                    order.deliveryLocations?.some(dl =>
                      dl.assignedTo?.username === user?.username &&
                      ['pickup_accepted', 'pickup_completed', 'en_route'].includes(dl.status || '')
                    )
                  ).map((order) => (
                    <div key={order._id} className="border rounded p-3">
                      <div className="row">
                        <div className="col-md-8">
                          <h6>{order.orderNumber} - {order.title}</h6>
                          <p><strong>{t('orders.client')}:</strong> {order.client}</p>

                          <div className="mb-3">
                            <strong>{t('orders.pickupLocation')}:</strong>
                            <div className="ms-3">
                              <div>{getPickupLocationAddress(order.pickupLocation)}</div>
                              {order.pickupLocation && typeof order.pickupLocation === 'object' && order.pickupLocation.coordinates && (
                                <small className="text-muted">
                                  📍 {order.pickupLocation.coordinates.lat.toFixed(4)}, {order.pickupLocation.coordinates.lng.toFixed(4)}
                                </small>
                              )}
                            </div>
                          </div>

                          <div className="mb-3">
                            <strong>{t('orders.deliveryLocations')}:</strong>
                            <div className="ms-3">
                              {order.deliveryLocations?.map((location, index) => (
                                <div key={location._id || index} className="border rounded p-2 mb-2">
                                  <div className="d-flex justify-content-between align-items-start">
                                    <div className="flex-grow-1">
                                      <div className="d-flex align-items-center mb-1">
                                        <span className={`badge ${location.status === 'completed' ? 'bg-success' : 'bg-secondary'} me-2`}>
                                          {location.status === 'completed' ? '✓' : index + 1}
                                        </span>
                                        <strong>{location.address}</strong>
                                      </div>
                                      {location.coordinates && (
                                        <small className="text-muted d-block">
                                          📍 {location.coordinates.lat.toFixed(4)}, {location.coordinates.lng.toFixed(4)}
                                        </small>
                                      )}
                                      {location.materials && location.materials.length > 0 && (
                                        <div className="mt-2">
                                          <small className="text-muted">{t('materials.materials')}:</small>
                                          <ul className="list-unstyled ms-2">
                                            {location.materials.map((material, matIndex) => (
                                              <li key={matIndex} className="small">
                                                • {material.quantityDelivered || 0}/{material.quantityOrdered} {material.unit} of {material.material}
                                                {material.isOverDelivered && <span className="text-warning ms-1">⚠️</span>}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}
                                    </div>
                                    {location.status === 'pickup_completed' && (currentLocation || developerMode) && (
                                      <button
                                        className="btn btn-sm btn-primary ms-2"
                                        onClick={() => handleDeliveryLocationSelect(location)}
                                      >
                                        {t('orders.completeDelivery')}
                                      </button>
                                    )}
                                    {location.status === 'pickup_accepted' && (
                                      <button
                                        className="btn btn-sm btn-success ms-2"
                                        onClick={() => handleCompletePickup(order._id, location._id)}
                                      >
                                        {t('orders.completePickup')}
                                      </button>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="col-md-4">
                          <span className={`badge ${getStatusBadgeClass(order.status)} mb-2`}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <p><strong>{t('orders.weight')}:</strong> {order.weight != null ? order.weight.toLocaleString() + ' lbs' : 'N/A'}</p>
                          <p><strong>{t('orders.haulingRate')}:</strong> {order.haulingRate != null ? '$' + order.haulingRate.toLocaleString() : 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  // Show available delivery locations
                  (() => {
                    const availableDeliveries = pendingOrders.flatMap(order =>
                      order.deliveryLocations
                        ?.filter(location => location.status === 'pending')
                        ?.map(location => ({ ...location, order })) || []
                    );

                    console.log('Available deliveries:', availableDeliveries);

                    return availableDeliveries.length === 0 ? (
                      <p className="text-muted">{t('orders.noPendingDeliveries')}</p>
                    ) : (
                      <div className="table-responsive">
                        <table className="table table-hover">
                          <thead className="d-none d-md-table-header-group">
                            <tr>
                              <th>{t('orders.orderNumber')}</th>
                              <th>{t('orders.client')}</th>
                              <th>{t('orders.pickupLocation')}</th>
                              <th>{t('orders.deliveryLocation')}</th>
                              <th>{t('orders.materialsNeeded')}</th>
                              <th>{t('orders.pickupDate')}</th>
                              <th>{t('orders.action')}</th>
                            </tr>
                          </thead>
                          <tbody className="d-none d-md-table-row-group">
                            {availableDeliveries.map((delivery) => (
                              <tr key={`${delivery.order._id}-${delivery._id}`}>
                                <td>{delivery.order.orderNumber}</td>
                                <td>{delivery.order.client}</td>
                                <td>{getPickupLocationAddress(delivery.order.pickupLocation)}</td>
                                <td>{delivery.address}</td>
                                <td>
                                  {delivery.materials && delivery.materials.length > 0 ? (
                                    <div>
                                      {delivery.materials.map((material, index) => (
                                        <div key={index} className="small">
                                          <strong>{material.quantityOrdered} {material.unit}</strong> of {material.material}
                                        </div>
                                      ))}
                                    </div>
                                  ) : (
                                    <span className="text-muted">{t('orders.noMaterialsSpecified')}</span>
                                  )}
                                </td>
                                <td>{new Date(delivery.order.pickupDate).toLocaleDateString()}</td>
                                <td>
                                  <button
                                    className="btn btn-success btn-sm"
                                    onClick={() => handleAcceptPickup(delivery.order._id, delivery._id)}
                                    disabled={!currentLocation && !developerMode}
                                  >
                                    <i className="bi bi-check-circle me-1"></i>
                                    {t('pickup.acceptPickup')}
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>

                        {/* Mobile Card Layout */}
                        <div className="d-md-none">
                          {availableDeliveries.map((delivery) => (
                            <div key={`mobile-${delivery.order._id}-${delivery._id}`} className="card mb-3">
                              <div className="card-body">
                                <div className="d-flex justify-content-between align-items-start mb-2">
                                  <h6 className="card-title mb-0">{t('orders.orderNumber')}: {delivery.order.orderNumber}</h6>
                                  <span className="badge bg-warning text-dark">{t('orders.unassigned')}</span>
                                </div>
                                <p className="card-text mb-1"><strong>{t('orders.client')}:</strong> {delivery.order.client}</p>
                                <p className="card-text mb-1"><strong>{t('orders.pickupLocation')}:</strong> {getPickupLocationAddress(delivery.order.pickupLocation)}</p>
                                <p className="card-text mb-1"><strong>{t('orders.deliveryLocation')}:</strong> {delivery.address}</p>
                                <p className="card-text mb-1"><strong>{t('orders.pickupDate')}:</strong> {new Date(delivery.order.pickupDate).toLocaleDateString()}</p>
                                <div className="mb-3">
                                  <strong>{t('orders.materialsNeeded')}:</strong>
                                  {delivery.materials && delivery.materials.length > 0 ? (
                                    <div className="mt-1">
                                      {delivery.materials.map((material, index) => (
                                        <div key={index} className="small border rounded p-1 mb-1 bg-light">
                                          <strong>{material.quantityOrdered} {material.unit}</strong> of {material.material}
                                        </div>
                                      ))}
                                    </div>
                                  ) : (
                                    <span className="text-muted ms-1">{t('orders.noMaterialsSpecified')}</span>
                                  )}
                                </div>
                                <button
                                  className="btn btn-success w-100"
                                  onClick={() => handleAcceptPickup(delivery.order._id, delivery._id)}
                                  disabled={!currentLocation && !developerMode}
                                >
                                  <i className="bi bi-check-circle me-1"></i>
                                  {t('pickup.acceptPickup')}
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })()
                )}
              </div>
            </div>
          </div>
        </div>

        {/* My Orders */}
        <div className="row">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-truck me-2"></i>
                  {t('orders.myOrders')}
                </h5>
              </div>
              <div className="card-body">
                {myOrders.length === 0 ? (
                  <p className="text-muted">{t('orders.noOrdersAvailable')}</p>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>{t('orders.orderNumber')}</th>
                          <th>{t('orders.client')}</th>
                          <th>{t('orders.title')}</th>
                          <th>{t('orders.route')}</th>
                          <th>{t('orders.status')}</th>
                          <th>{t('orders.pickupDate')}</th>
                          <th>{t('orders.deliveryDate')}</th>
                          <th>{t('orders.haulingRate')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {myOrders.map((order) => (
                          <tr key={order._id}>
                            <td>{order.orderNumber}</td>
                            <td>{order.client}</td>
                            <td>{order.title}</td>
                            <td>
                              {getPickupLocationAddress(order.pickupLocation)} → {getDeliveryLocationsDisplay(order.deliveryLocations)}
                            </td>
                            <td>
                              <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                                {order.status.replace('_', ' ').toUpperCase()}
                              </span>
                            </td>
                            <td>{new Date(order.pickupDate).toLocaleDateString()}</td>
                            <td>{new Date(order.deliveryDate).toLocaleDateString()}</td>
                            <td>{order.haulingRate != null ? `$${order.haulingRate.toLocaleString()}` : 'N/A'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
          </>
        )}

        {/* Transactions Tab */}
        {activeTab === 'transactions' && (
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">
                <i className="bi bi-receipt me-2"></i>
                {t('transactions.myTransactions')}
              </h5>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>{t('transactions.date')}</th>
                      <th>{t('transactions.orderNumber')}</th>
                      <th>{t('transactions.deliveryAddress')}</th>
                      <th>{t('transactions.materialsDelivered')}</th>
                      <th>{t('transactions.totalEarned')}</th>
                      <th>{t('transactions.actions')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="text-center text-muted">
                          {t('transactions.noTransactionsFound')}
                        </td>
                      </tr>
                    ) : (
                      transactions.map((transaction) => (
                        <tr key={transaction._id}>
                          <td>
                            <div>{new Date(transaction.completedAt).toLocaleDateString()}</div>
                            <small className="text-muted">{new Date(transaction.completedAt).toLocaleTimeString()}</small>
                          </td>
                          <td>{transaction.orderNumber || 'N/A'}</td>
                          <td>{transaction.deliveryAddress || 'N/A'}</td>
                          <td>
                            {transaction.materialsDelivered && transaction.materialsDelivered.length > 0 ?
                              transaction.materialsDelivered.map((material, index) => (
                                <div key={index} className="small">
                                  {material.quantityDelivered || 0} {material.unit || ''} of {material.material || 'Unknown'}
                                </div>
                              ))
                              : <span className="text-muted">No materials</span>
                            }
                          </td>
                          <td>
                            <strong>
                              {transaction.materialsDelivered && transaction.materialsDelivered.length > 0 ?
                                transaction.materialsDelivered.reduce((total, material) =>
                                  total + ((material.quantityDelivered || 0) * (material.ratePerUnit || 0)), 0
                                ).toLocaleString('en-US', { style: 'currency', currency: 'USD' })
                                : '$0.00'
                              }
                            </strong>
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <button
                                className="btn btn-sm btn-outline-primary"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">Delivery Photo</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.deliveryPhoto}" style="max-width: 100%; max-height: 70vh;" class="img-fluid" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title="View Photo"
                              >
                                <i className="bi bi-image"></i>
                              </button>
                              <button
                                className="btn btn-sm btn-outline-secondary"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">Delivery Signature</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.signature}" style="max-width: 100%; max-height: 50vh;" class="img-fluid border" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title="View Signature"
                              >
                                <i className="bi bi-pen"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>



      {/* Delivery Completion Modal */}
      {showDeliveryModal && selectedDeliveryLocation && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg modal-dialog-scrollable">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">{t('delivery.completeDeliveryFor')} {selectedDeliveryLocation.address}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeliveryModal(false);
                    setSelectedDeliveryLocation(null);
                    setDeliveryPhoto('');
                    setDeliverySignature('');
                    setActualQuantities([]);
                    setDeliveryNotes('');
                    if (deliverySignatureRef.current) {
                      deliverySignatureRef.current.clear();
                    }
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <h6>{t('orders.deliveryLocation')}:</h6>
                <p>{selectedDeliveryLocation.address}</p>

                {/* Materials to Deliver Section */}
                <div className="mb-4">
                  <h6 className="mb-3">{t('materials.materialsToDeliver')}</h6>
                  {selectedDeliveryLocation.materials && selectedDeliveryLocation.materials.length > 0 ? (
                    selectedDeliveryLocation.materials.map((material, index) => (
                      <div key={index} className="row mb-3 align-items-center border rounded p-3">
                        <div className="col-md-4">
                          <label className="form-label fw-bold">{t('materials.material')}</label>
                          <div className="form-control-plaintext">{material.material}</div>
                        </div>
                        <div className="col-md-3">
                          <label className="form-label fw-bold">{t('materials.quantityOrdered')}</label>
                          <div className="form-control-plaintext">{material.quantityOrdered} {material.unit}</div>
                        </div>
                        <div className="col-md-3">
                          <label className="form-label fw-bold">{t('materials.alreadyDelivered')}</label>
                          <div className="form-control-plaintext">{material.quantityDelivered || 0} {material.unit}</div>
                        </div>
                        <div className="col-md-2">
                          <label className="form-label fw-bold">{t('materials.remaining')}</label>
                          <div className="form-control-plaintext">
                            {(material.quantityOrdered - (material.quantityDelivered || 0)).toFixed(1)} {material.unit}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">{t('materials.noMaterialsSpecified')}</p>
                  )}
                </div>

                {/* Actual Quantities Delivered Section */}
                <div className="mb-4">
                  <h6 className="mb-3">{t('materials.actualQuantitiesDelivered')} *</h6>
                  {selectedDeliveryLocation.materials && selectedDeliveryLocation.materials.length > 0 ? (
                    selectedDeliveryLocation.materials.map((material, index) => (
                      <div key={index} className="row mb-3 align-items-center">
                        <div className="col-md-6">
                          <label className="form-label">{material.material}</label>
                        </div>
                        <div className="col-md-4">
                          <input
                            type="number"
                            className="form-control"
                            placeholder="0"
                            value={actualQuantities[index] || ''}
                            onChange={(e) => updateActualQuantity(index, e.target.value)}
                            min="0"
                            step="0.1"
                            required
                          />
                        </div>
                        <div className="col-md-2">
                          <span className="form-control-plaintext">{material.unit}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">{t('materials.noMaterialsToDeliver')}</p>
                  )}
                </div>

                {/* Notes Section */}
                <div className="mb-4">
                  <label className="form-label">{t('delivery.notes')} ({t('delivery.optionalNotes')})</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    value={deliveryNotes}
                    onChange={(e) => setDeliveryNotes(e.target.value)}
                    placeholder="Any additional notes about this delivery..."
                  ></textarea>
                </div>



                {/* Delivery Section */}
                <div className="row">
                  <div className="col-12">
                    <h5 className="text-success">{t('delivery.deliveryPhoto')} {t('delivery.signature')}</h5>
                  </div>
                  <div className="col-md-6">
                    <h6>{t('delivery.uploadPhoto')} *</h6>
                    <input
                      type="file"
                      className="form-control mb-3"
                      accept="image/*"
                      onChange={handleDeliveryPhotoUpload}
                    />
                    {deliveryPhoto && (
                      <div className="mb-3">
                        <img
                          src={deliveryPhoto}
                          alt="Delivery"
                          style={{ maxWidth: '100%', maxHeight: '200px' }}
                          className="border rounded"
                        />
                      </div>
                    )}
                  </div>

                  <div className="col-md-6">
                    <h6>{t('delivery.signature')} *</h6>
                    <div className="border rounded mb-2" style={{ height: '200px' }}>
                      <SignatureCanvas
                        ref={deliverySignatureRef}
                        canvasProps={{
                          width: 300,
                          height: 200,
                          className: 'signature-canvas'
                        }}
                        onEnd={saveDeliverySignature}
                      />
                    </div>
                    <div className="d-flex gap-2">
                      <button
                        className="btn btn-sm btn-outline-secondary"
                        onClick={clearDeliverySignature}
                      >
                        {t('delivery.clearSignature')}
                      </button>
                      {deliverySignature && (
                        <small className="text-success align-self-center">✓ {t('delivery.signature')} captured</small>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeliveryModal(false);
                    setSelectedDeliveryLocation(null);
                    setDeliveryPhoto('');
                    setDeliverySignature('');
                    setActualQuantities([]);
                    setDeliveryNotes('');
                    if (deliverySignatureRef.current) {
                      deliverySignatureRef.current.clear();
                    }
                  }}
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={handleCompleteDelivery}
                  disabled={!deliveryPhoto || !deliverySignature}
                >
                  <i className="bi bi-check-circle me-1"></i>
                  {t('orders.completeDelivery')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pickup Modal */}
      {showPickupModal && selectedOrder && selectedDeliveryLocation && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg modal-dialog-scrollable">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">{t('pickup.acceptPickupFor')} {selectedDeliveryLocation.address}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowPickupModal(false);
                    setSelectedOrder(null);
                    setSelectedDeliveryLocation(null);
                    setPickupPhoto('');
                    setPickupSignature('');
                    if (pickupSignatureRef.current) {
                      pickupSignatureRef.current.clear();
                    }
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <h6>{t('orders.orderNumber')}: {selectedOrder.orderNumber}</h6>
                <p><strong>{t('orders.client')}:</strong> {selectedOrder.client}</p>
                <p><strong>{t('orders.pickupLocation')}:</strong> {getPickupLocationAddress(selectedOrder.pickupLocation)}</p>
                <p><strong>{t('orders.deliveryLocation')}:</strong> {selectedDeliveryLocation.address}</p>

                {/* Materials Section */}
                {selectedDeliveryLocation.materials && selectedDeliveryLocation.materials.length > 0 && (
                  <div className="mb-4">
                    <h6 className="mb-3">{t('materials.materialsToDeliver')}</h6>
                    {selectedDeliveryLocation.materials.map((material, index) => (
                      <div key={index} className="row mb-2 align-items-center border rounded p-2">
                        <div className="col-md-6">
                          <strong>{material.material}</strong>
                        </div>
                        <div className="col-md-3">
                          {material.quantityOrdered} {material.unit}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Pickup Documentation */}
                <div className="row">
                  <div className="col-12">
                    <h5 className="text-primary">{t('pickup.pickupDocumentation')}</h5>
                  </div>
                  <div className="col-12 col-md-6 mb-3">
                    <h6>{t('pickup.uploadPickupPhoto')} *</h6>
                    <input
                      type="file"
                      className="form-control mb-3"
                      accept="image/*"
                      onChange={handlePickupPhotoUpload}
                    />
                    {pickupPhoto && (
                      <div className="mb-3">
                        <img
                          src={pickupPhoto}
                          alt="Pickup"
                          style={{ maxWidth: '100%', maxHeight: '200px' }}
                          className="border rounded"
                        />
                      </div>
                    )}
                  </div>

                  <div className="col-12 col-md-6">
                    <h6>{t('pickup.pickupSignature')} *</h6>
                    <div className="border rounded mb-2" style={{ height: '200px', width: '100%' }}>
                      <SignatureCanvas
                        ref={pickupSignatureRef}
                        canvasProps={{
                          width: window.innerWidth < 768 ? 280 : 300,
                          height: 200,
                          className: 'signature-canvas w-100'
                        }}
                        onEnd={savePickupSignature}
                      />
                    </div>
                    <div className="d-flex flex-column flex-sm-row gap-2">
                      <button
                        className="btn btn-sm btn-outline-secondary"
                        onClick={clearPickupSignature}
                      >
                        {t('pickup.clearPickupSignature')}
                      </button>
                      {pickupSignature && (
                        <small className="text-success align-self-center">{t('pickup.pickupSignatureCaptured')}</small>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowPickupModal(false);
                    setSelectedOrder(null);
                    setSelectedDeliveryLocation(null);
                    setPickupPhoto('');
                    setPickupSignature('');
                    if (pickupSignatureRef.current) {
                      pickupSignatureRef.current.clear();
                    }
                  }}
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={handleSubmitPickup}
                  disabled={!pickupPhoto || !pickupSignature}
                >
                  <i className="bi bi-check-circle me-1"></i>
                  {t('pickup.acceptPickup')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TruckerDashboard;
